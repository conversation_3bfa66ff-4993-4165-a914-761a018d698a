#!/usr/bin/env python3
"""
Тест пагинации истории пробных ЕНТ
"""

import asyncio
import logging
from datetime import datetime

# Настраиваем логирование
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
)

# Мок FSM контекста
class MockFSMContext:
    def __init__(self):
        self.data = {}
    
    async def get_data(self):
        return self.data
    
    async def update_data(self, **kwargs):
        self.data.update(kwargs)
    
    async def set_data(self, data):
        self.data = data

async def test_trial_ent_pagination():
    """Тест пагинации истории пробных ЕНТ"""
    print("🧪 ТЕСТ: Пагинация истории пробных ЕНТ с логированием")
    print("=" * 70)
    
    try:
        from student.keyboards.trial_ent import create_trial_ent_history_keyboard, _get_page_text_for_history
        
        state = MockFSMContext()
        
        # Создаем тестовые данные (5 тестов для проверки пагинации)
        test_history = [
            {
                'id': i,
                'required_subjects': ['kz'],
                'profile_subjects': ['math'] if i % 2 == 0 else [],
                'total_questions': 30,
                'correct_answers': 15 + i * 2,
                'percentage': 50 + i * 10,
                'completed_at': datetime(2025, 7, 21 - i, 10, 0)
            }
            for i in range(1, 6)  # 5 тестов
        ]
        
        print(f"📊 Создано {len(test_history)} тестовых записей")
        
        # Тестируем создание клавиатуры
        print("\n🔧 Создание клавиатуры...")
        keyboard, initial_text = await create_trial_ent_history_keyboard(test_history, state)
        
        initial_test_count = initial_text.count('📊')
        print(f"✅ Клавиатура создана")
        print(f"✅ Начальный текст содержит: {initial_test_count} тестов")
        print(f"✅ Клавиатура имеет: {len(keyboard.inline_keyboard)} рядов")
        
        # Проверяем FSM
        fsm_data = await state.get_data()
        saved_history = fsm_data.get('trial_ent_full_history', [])
        print(f"✅ В FSM сохранено: {len(saved_history)} тестов")
        
        # Тестируем страницы
        print("\n📄 Тестирование страниц:")
        
        for page in range(1, 3):
            page_text = _get_page_text_for_history(test_history, page=page, per_page=3)
            page_test_count = page_text.count('📊')
            print(f"  Страница {page}: {page_test_count} тестов")
        
        print("\n" + "=" * 70)
        print("🎉 ТЕСТ ЗАВЕРШЕН УСПЕШНО!")
        print("✅ Все функции работают корректно")
        print("✅ Логирование настроено")
        print("✅ Пагинация функционирует")
        
    except Exception as e:
        print(f"❌ ОШИБКА В ТЕСТЕ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_trial_ent_pagination())
