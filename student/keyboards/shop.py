from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button
from typing import List
from datetime import datetime


def format_date_russian(date: datetime) -> str:
    """Форматировать дату на русском языке"""
    months = {
        1: 'января', 2: 'февраля', 3: 'марта', 4: 'апреля',
        5: 'мая', 6: 'июня', 7: 'июля', 8: 'августа',
        9: 'сентября', 10: 'октября', 11: 'ноября', 12: 'декабря'
    }
    return f"{date.day} {months[date.month]}"

def get_shop_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура главного меню магазина"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="💳 Обменять баллы", callback_data="exchange_points")],
        [InlineKeyboardButton(text="🛒 Каталог бонусов", callback_data="bonus_catalog")],
        [InlineKeyboardButton(text="📦 Мои бонусы", callback_data="my_bonuses")],
        *get_main_menu_back_button()
    ])

async def get_exchange_points_kb(available_points: int) -> InlineKeyboardMarkup:
    """Клавиатура для обмена баллов на монеты"""
    buttons = []

    # Варианты обмена в зависимости от доступных баллов
    exchange_options = [50, 70, 100, 150, 200]

    for amount in exchange_options:
        if available_points >= amount:
            buttons.append([InlineKeyboardButton(
                text=f"{amount} баллов → {amount} монет",
                callback_data=f"exchange_{amount}"
            )])

    if not buttons:
        buttons.append([InlineKeyboardButton(
            text="❌ Недостаточно баллов",
            callback_data="no_points"
        )])

    buttons.extend(get_main_menu_back_button())
    return InlineKeyboardMarkup(inline_keyboard=buttons)

async def create_bonus_catalog_keyboard(items: List, user_coins: int, state) -> InlineKeyboardMarkup:
    """Клавиатура каталога бонусов с умной пагинацией"""
    try:
        # Преобразуем товары в кнопки
        buttons = []
        for item in items:
            # Определяем эмодзи по типу товара
            emoji = {
                'bonus_test': '🧪',
                'bonus_task': '🎯',
                'pdf': '📘',
                'money': '💰',
                'other': '🎁'
            }.get(item['item_type'], '🎁')

            # Определяем callback_data в зависимости от типа товара
            if item['type'] == 'bonus_test':
                callback_prefix = "buy_bonus_"
            else:
                callback_prefix = "buy_item_"

            # Проверяем, хватает ли монет
            if user_coins >= item['price']:
                button_text = f"{emoji} {item['name']} — {item['price']} монет"
                callback_data = f"{callback_prefix}{item['id']}"
            else:
                button_text = f"❌ {emoji} {item['name']} — {item['price']} монет"
                callback_data = f"no_coins_{item['id']}"

            button = InlineKeyboardButton(
                text=button_text,
                callback_data=callback_data
            )
            buttons.append(button)

        # Если товаров мало, создаем обычную клавиатуру
        if len(buttons) <= 6:
            keyboard_buttons = []
            for button in buttons:
                keyboard_buttons.append([button])
            keyboard_buttons.extend(get_main_menu_back_button())
            return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        # Если товаров много, создаем пагинатор и возвращаем первую страницу
        from common.pagination import Paginator
        paginator = Paginator(
            state=state,
            data=buttons,
            per_page=6,  # 6 товаров на страницу
            per_row=1,   # По одному товару в ряду
            lang="ru"    # Русский язык
        )

        # Сохраняем пагинатор в FSM
        await paginator.save_to_fsm()

        # Возвращаем клавиатуру первой страницы
        return await paginator.render_kb(page=1)

    except Exception as e:
        print(f"Ошибка при создании клавиатуры каталога бонусов: {e}")
        # В случае ошибки возвращаем клавиатуру с кнопкой назад
        return InlineKeyboardMarkup(inline_keyboard=[
            *get_main_menu_back_button()
        ])




async def create_my_bonuses_keyboard(purchases: List, bonus_test_purchases: List, state) -> InlineKeyboardMarkup:
    """Клавиатура для раздела 'Мои покупки' с умной пагинацией"""
    try:
        # Преобразуем покупки в кнопки
        buttons = []

        # Добавляем кнопки для обычных товаров (бонусные задания)
        for purchase in purchases:
            # Определяем эмодзи по типу товара
            emoji = {
                'bonus_task': '🎯',
                'pdf': '📘',
                'money': '💰',
                'other': '🎁'
            }.get(purchase.item.item_type, '🎁')

            # Форматируем дату
            date_str = format_date_russian(purchase.purchased_at)

            button_text = f"{emoji} {purchase.item.name} — {purchase.price_paid} монет — {date_str}"
            callback_data = f"use_bonus_{purchase.id}"

            button = InlineKeyboardButton(
                text=button_text,
                callback_data=callback_data
            )
            buttons.append(button)

        # Добавляем кнопки для бонусных тестов
        for purchase in bonus_test_purchases:
            question_count = len(purchase.bonus_test.questions) if purchase.bonus_test.questions else 0
            date_str = format_date_russian(purchase.purchased_at)

            button_text = f"🧪 {purchase.bonus_test.name} — {purchase.price_paid} монет — {date_str}"
            callback_data = f"use_bonus_test_{purchase.id}"

            button = InlineKeyboardButton(
                text=button_text,
                callback_data=callback_data
            )
            buttons.append(button)

        # Если покупок мало, создаем обычную клавиатуру
        if len(buttons) <= 5:
            keyboard_buttons = []
            for button in buttons:
                keyboard_buttons.append([button])
            keyboard_buttons.extend(get_main_menu_back_button())
            return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        # Если покупок много, создаем пагинатор и возвращаем первую страницу
        from common.pagination import Paginator
        paginator = Paginator(
            state=state,
            data=buttons,
            per_page=5,  # 5 покупок на страницу
            per_row=1,   # По одной покупке в ряду
            lang="ru"    # Русский язык
        )

        # Сохраняем пагинатор в FSM
        await paginator.save_to_fsm()

        # Возвращаем клавиатуру первой страницы
        return await paginator.render_kb(page=1)

    except Exception as e:
        print(f"Ошибка при создании клавиатуры моих бонусов: {e}")
        # В случае ошибки возвращаем клавиатуру с кнопкой назад
        return InlineKeyboardMarkup(inline_keyboard=[
            *get_main_menu_back_button()
        ])




def get_back_to_shop_kb() -> InlineKeyboardMarkup:
    """Клавиатура для возврата в меню магазина"""
    return InlineKeyboardMarkup(inline_keyboard=[
        *get_main_menu_back_button(),
    ])

def get_purchase_confirmation_kb(item_id: int) -> InlineKeyboardMarkup:
    """Клавиатура подтверждения покупки бонусного теста"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="✅ Подтвердить покупку", callback_data=f"confirm_purchase_bonus_{item_id}")],
        [InlineKeyboardButton(text="🛒 Вернуться к каталогу", callback_data="bonus_catalog")],
        [InlineKeyboardButton(text="❌ Отмена", callback_data="cancel_purchase")],
        *get_main_menu_back_button()
    ])

def get_item_purchase_confirmation_kb(item_id: int) -> InlineKeyboardMarkup:
    """Клавиатура подтверждения покупки обычного товара (бонусного задания)"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="✅ Подтвердить покупку", callback_data=f"confirm_purchase_item_{item_id}")],
        [InlineKeyboardButton(text="🛒 Вернуться к каталогу", callback_data="bonus_catalog")],
        [InlineKeyboardButton(text="❌ Отмена", callback_data="cancel_purchase")],
        *get_main_menu_back_button()
    ])