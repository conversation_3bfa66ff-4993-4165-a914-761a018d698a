from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button, get_universal_back_button
from typing import List, Dict, Any, <PERSON><PERSON>


def get_trial_ent_start_kb() -> InlineKeyboardMarkup:
    """Клавиатура для начала пробного ЕНТ"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="▶️ Начать пробный ЕНТ", callback_data="start_trial_ent")],
        [InlineKeyboardButton(text="📊 Аналитика прошлых тестов", callback_data="view_analytics")],
        *get_main_menu_back_button()
    ])

def get_required_subjects_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора обязательных предметов"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="История Казахстана", callback_data="req_sub_kz")],
        [InlineKeyboardButton(text="Математическая грамотность", callback_data="req_sub_mathlit")],
        [InlineKeyboardButton(text="История Казахстана и Математическая грамотность", callback_data="req_sub_both")],
        *get_main_menu_back_button()
    ])

def get_profile_subjects_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора профильных предметов"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="Математика", callback_data="prof_sub_math")],
        [InlineKeyboardButton(text="География", callback_data="prof_sub_geo")],
        [InlineKeyboardButton(text="Биология", callback_data="prof_sub_bio")],
        [InlineKeyboardButton(text="Химия", callback_data="prof_sub_chem")],
        [InlineKeyboardButton(text="Информатика", callback_data="prof_sub_inf")],
        [InlineKeyboardButton(text="Всемирная история", callback_data="prof_sub_world")],
        [InlineKeyboardButton(text="Нет профильных предметов", callback_data="prof_sub_none")],
        *get_main_menu_back_button()
    ])

def get_second_profile_subject_kb(first_subject: str) -> InlineKeyboardMarkup:
    """Клавиатура выбора второго профильного предмета"""
    buttons = []

    # Словарь соответствия callback_data и названий предметов
    subjects = {
        "prof_sub_math": "Математика",
        "prof_sub_geo": "География",
        "prof_sub_bio": "Биология",
        "prof_sub_chem": "Химия",
        "prof_sub_inf": "Информатика",
        "prof_sub_world": "Всемирная история"
    }

    # Добавляем все предметы, кроме уже выбранного
    for callback, name in subjects.items():
        if callback != first_subject:
            buttons.append([InlineKeyboardButton(text=name, callback_data=f"second_{callback}")])

    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)

def get_trial_ent_confirmation_kb() -> InlineKeyboardMarkup:
    """Клавиатура подтверждения начала пробного ЕНТ"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="🚀 Начать тест", callback_data="start_trial_ent_test")],
        *get_main_menu_back_button()
    ])



def get_after_trial_ent_kb() -> InlineKeyboardMarkup:
    """Клавиатура после завершения пробного ЕНТ"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📊 Посмотреть аналитику", callback_data="view_analytics")],
        [InlineKeyboardButton(text="🔄 Пройти ещё раз", callback_data="retry_trial_ent")],
        *get_main_menu_back_button()
    ])

def get_analytics_subjects_kb(subjects: list) -> InlineKeyboardMarkup:
    """Клавиатура выбора предмета для просмотра аналитики"""
    buttons = []
    
    for subject in subjects:
        if subject == "kz":
            buttons.append([InlineKeyboardButton(text="История Казахстана", callback_data="analytics_kz")])
        elif subject == "mathlit":
            buttons.append([InlineKeyboardButton(text="Математическая грамотность", callback_data="analytics_mathlit")])
        elif subject == "math":
            buttons.append([InlineKeyboardButton(text="Математика", callback_data="analytics_math")])
        elif subject == "geo":
            buttons.append([InlineKeyboardButton(text="География", callback_data="analytics_geo")])
        elif subject == "bio":
            buttons.append([InlineKeyboardButton(text="Биология", callback_data="analytics_bio")])
        elif subject == "chem":
            buttons.append([InlineKeyboardButton(text="Химия", callback_data="analytics_chem")])
        elif subject == "inf":
            buttons.append([InlineKeyboardButton(text="Информатика", callback_data="analytics_inf")])
        elif subject == "world":
            buttons.append([InlineKeyboardButton(text="Всемирная история", callback_data="analytics_world")])
    
    buttons.extend(get_main_menu_back_button())
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)

def get_back_to_analytics_kb() -> InlineKeyboardMarkup:
    """Клавиатура для возврата к выбору предмета для аналитики"""
    return InlineKeyboardMarkup(inline_keyboard=[
        *get_main_menu_back_button()
    ])

def get_trial_ent_analytics_menu_kb(subject_code: str) -> InlineKeyboardMarkup:
    """Клавиатура выбора типа аналитики для пробного ЕНТ"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(
            text="📈 % правильных ответов по темам",
            callback_data=f"trial_ent_detailed_{subject_code}"
        )],
        [InlineKeyboardButton(
            text="🟢🔴 Сильные и слабые темы",
            callback_data=f"trial_ent_summary_{subject_code}"
        )],
        *get_main_menu_back_button()
    ])


async def create_trial_ent_history_keyboard(history: List[Dict[str, Any]], state) -> Tuple[InlineKeyboardMarkup, str]:
    """
    Клавиатура истории пробных ЕНТ с умной пагинацией

    Возвращает кортеж (клавиатура, текст_сообщения)
    При пагинации синхронизирует текст и кнопки для текущей страницы
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"🔧 CREATE_TRIAL_ENT_KEYBOARD: Начинаем создание клавиатуры для {len(history)} тестов")
        from common.trial_ent_service import TrialEntService

        # Преобразуем историю в кнопки
        buttons = []
        for i, result in enumerate(history, 1):
            button = InlineKeyboardButton(
                text=f"📊 Тест {i} ({result['percentage']}%)",
                callback_data=f"history_detail_{result['id']}"
            )
            buttons.append(button)

        # Если тестов мало, создаем обычную клавиатуру и полный текст
        if len(buttons) <= 3:
            keyboard_buttons = []
            for button in buttons:
                keyboard_buttons.append([button])
            keyboard_buttons.extend(get_main_menu_back_button())

            # Формируем полный текст истории
            history_text = "📈 История ваших пробных ЕНТ:\n\n"
            for i, result in enumerate(history, 1):
                date_str = result["completed_at"].strftime("%d.%m.%Y %H:%M")
                all_subjects = result["required_subjects"] + result["profile_subjects"]
                subjects_text = ", ".join([TrialEntService.get_subject_name(code) for code in all_subjects])

                history_text += f"{i}. {date_str}\n"
                history_text += f"   📊 {result['correct_answers']}/{result['total_questions']} ({result['percentage']}%)\n"
                history_text += f"   📚 {subjects_text}\n\n"

            return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons), history_text

        # Если тестов много, создаем пагинатор
        from common.pagination import Paginator

        # Сохраняем полные данные истории в FSM для использования при навигации
        await state.update_data(trial_ent_full_history=history)

        paginator = Paginator(
            state=state,
            data=buttons,
            per_page=3,  # 3 теста на страницу
            per_row=1,   # По одному тесту в ряду
            lang="ru"    # Русский язык
        )

        # Сохраняем пагинатор в FSM
        await paginator.save_to_fsm()

        # Возвращаем клавиатуру и текст первой страницы
        keyboard = await paginator.render_kb(page=1)
        text = _get_page_text_for_history(history, page=1, per_page=3)

        return keyboard, text

    except Exception as e:
        print(f"Ошибка при создании клавиатуры истории пробных ЕНТ: {e}")
        # В случае ошибки возвращаем клавиатуру с кнопкой назад и базовый текст
        return InlineKeyboardMarkup(inline_keyboard=[
            *get_main_menu_back_button()
        ]), "❌ Ошибка при загрузке истории"


def _get_page_text_for_history(history: List[Dict[str, Any]], page: int, per_page: int = 3) -> str:
    """Получить текст для конкретной страницы истории"""
    from common.trial_ent_service import TrialEntService

    # Вычисляем индексы для текущей страницы
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    page_history = history[start_idx:end_idx]

    # Формируем текст для текущей страницы
    history_text = "📈 История ваших пробных ЕНТ:\n\n"

    for i, result in enumerate(page_history, start_idx + 1):
        date_str = result["completed_at"].strftime("%d.%m.%Y %H:%M")
        all_subjects = result["required_subjects"] + result["profile_subjects"]
        subjects_text = ", ".join([TrialEntService.get_subject_name(code) for code in all_subjects])

        history_text += f"{i}. {date_str}\n"
        history_text += f"   📊 {result['correct_answers']}/{result['total_questions']} ({result['percentage']}%)\n"
        history_text += f"   📚 {subjects_text}\n\n"

    return history_text